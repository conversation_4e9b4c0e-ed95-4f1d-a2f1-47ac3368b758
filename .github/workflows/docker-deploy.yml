name: Docker Build and Deploy

on:
  push:
    branches:
      - master

env:
  DOCKER_IMAGE: moviebox-api
  DOCKER_TAG: latest

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download milvus_lite.db from Google Drive
        run: |
          # Create data/milvus directory
          mkdir -p data/milvus

          # Download milvus_lite.db from Google Drive
          # Replace GOOGLE_DRIVE_FILE_ID with your actual Google Drive file ID
          FILE_ID="${{ secrets.MILVUS_DB_FILE_ID }}"
          curl -L "https://drive.google.com/uc?export=download&id=${FILE_ID}" -o data/milvus/milvus_lite.db

          # Verify the file was downloaded
          if [ -f "data/milvus/milvus_lite.db" ]; then
            echo "milvus_lite.db downloaded successfully"
            ls -la data/milvus/milvus_lite.db
          else
            echo "Failed to download milvus_lite.db"
            exit 1
          fi

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Remove existing Docker images from Docker Hub
        run: |
          # Get all tags for the repository
          REPO_NAME="${{ secrets.DOCKER_USERNAME }}/${{ env.DOCKER_IMAGE }}"

          # Get authentication token for Docker Hub API
          TOKEN=$(curl -s -H "Content-Type: application/json" -X POST -d '{"username": "${{ secrets.DOCKER_USERNAME }}", "password": "${{ secrets.DOCKER_PASSWORD }}"}' https://hub.docker.com/v2/users/login/ | jq -r .token)

          echo "=== Deleting repository to clean up all manifests ==="
          # The most reliable way to clean up all manifests and untagged images
          # is to delete the entire repository and let it be recreated on next push
          DELETE_RESPONSE=$(curl -s -X DELETE -H "Authorization: JWT ${TOKEN}" "https://hub.docker.com/v2/repositories/${REPO_NAME}/")

          if echo "$DELETE_RESPONSE" | grep -q "error\|Error"; then
            echo "Repository deletion failed or repository doesn't exist: $DELETE_RESPONSE"
            echo "This is normal if the repository is already empty"
          else
            echo "Repository deleted successfully - all images and manifests cleaned up"
          fi

          echo "=== Docker Hub cleanup completed ==="

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ secrets.DOCKER_USERNAME }}/${{ env.DOCKER_IMAGE }}:${{ env.DOCKER_TAG }}

      - name: Deploy to VPS
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          key: ${{ secrets.VPS_SSH_KEY }}
          script: |
            # Create .env file
            cat > .env << EOL
            SUPABASE_URL=${{ secrets.SUPABASE_URL }}
            SUPABASE_KEY=${{ secrets.SUPABASE_KEY }}
            REDIS_URL=redis://redis:6379
            KAGGLE_USERNAME=${{ secrets.KAGGLE_USERNAME }}
            KAGGLE_KEY=${{ secrets.KAGGLE_KEY }}
            EMBEDDINGS_MODEL=sentence-transformers/all-MiniLM-L6-v2
            EMBEDDINGS_MODEL_REVISION=8b3219a92973c328a8e22fadcfa821b5dc75636a
            EOL

            # Create docker-compose.yml file
            cat > docker-compose.yml << EOL
            version: '3.8'

            services:
              api:
                image: ${{ secrets.DOCKER_USERNAME }}/${{ env.DOCKER_IMAGE }}:${{ env.DOCKER_TAG }}
                ports:
                  - '8000:8000'
                environment:
                  - REDIS_URL=redis://redis:6379
                  - SUPABASE_URL=\${SUPABASE_URL}
                  - SUPABASE_KEY=\${SUPABASE_KEY}
                  - KAGGLE_USERNAME=\${KAGGLE_USERNAME}
                  - KAGGLE_KEY=\${KAGGLE_KEY}
                  - EMBEDDINGS_MODEL=sentence-transformers/all-MiniLM-L6-v2
                  - EMBEDDINGS_MODEL_REVISION=8b3219a92973c328a8e22fadcfa821b5dc75636a
                depends_on:
                  - redis
                volumes:
                  - milvus_data:/app/data/milvus:rw

              redis:
                image: redis:7-alpine
                ports:
                  - '6379:6379'
                volumes:
                  - redis_data:/data

            volumes:
              redis_data:
              milvus_data:
            EOL


            # Install Docker Compose if not already installed
            if ! command -v docker-compose &> /dev/null; then
              sudo curl -L "https://github.com/docker/compose/releases/download/v2.24.6/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
              sudo chmod +x /usr/local/bin/docker-compose
            fi

            # Stop and remove existing containers
            # docker-compose down || true

            # # Clean up old images
            # docker system prune -a || true

            # # Pull the latest image
            # docker pull ${{ secrets.DOCKER_USERNAME }}/${{ env.DOCKER_IMAGE }}:${{ env.DOCKER_TAG }}

            # # Start the services using docker-compose
            # docker-compose up -d
